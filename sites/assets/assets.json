{"billing.bundle.js": "/assets/frappe/dist/js/billing.bundle.EM6VB57J.js", "bootstrap-4-web.bundle.js": "/assets/frappe/dist/js/bootstrap-4-web.bundle.AZ67VXZX.js", "controls.bundle.js": "/assets/frappe/dist/js/controls.bundle.UGHAI536.js", "data_import_tools.bundle.js": "/assets/frappe/dist/js/data_import_tools.bundle.OT4HV4C6.js", "desk.bundle.js": "/assets/frappe/dist/js/desk.bundle.TZL3CAGQ.js", "dialog.bundle.js": "/assets/frappe/dist/js/dialog.bundle.6NPBIXVE.js", "form.bundle.js": "/assets/frappe/dist/js/form.bundle.TVTHQIMW.js", "frappe-web.bundle.js": "/assets/frappe/dist/js/frappe-web.bundle.M4ZMZZX7.js", "libs.bundle.js": "/assets/frappe/dist/js/libs.bundle.LLRFRX7M.js", "list.bundle.js": "/assets/frappe/dist/js/list.bundle.R4RCQCIS.js", "logtypes.bundle.js": "/assets/frappe/dist/js/logtypes.bundle.MJKW7EK3.js", "onboarding_tours.bundle.js": "/assets/frappe/dist/js/onboarding_tours.bundle.P7QYMXLW.js", "report.bundle.js": "/assets/frappe/dist/js/report.bundle.FNMRTS3V.js", "sentry.bundle.js": "/assets/frappe/dist/js/sentry.bundle.SI3DB3BY.js", "telemetry.bundle.js": "/assets/frappe/dist/js/telemetry.bundle.ZJBT5ETW.js", "user_profile_controller.bundle.js": "/assets/frappe/dist/js/user_profile_controller.bundle.TAMQL3L3.js", "video_player.bundle.js": "/assets/frappe/dist/js/video_player.bundle.IOEIXC2G.js", "web_form.bundle.js": "/assets/frappe/dist/js/web_form.bundle.5SD2BBYX.js", "form_builder.bundle.js": "/assets/frappe/dist/js/form_builder.bundle.WKUWDLHZ.js", "print_format_builder.bundle.js": "/assets/frappe/dist/js/print_format_builder.bundle.ZLPLI4NJ.js", "workflow_builder.bundle.js": "/assets/frappe/dist/js/workflow_builder.bundle.XQC2PMWS.js", "build_events.bundle.js": "/assets/frappe/dist/js/build_events.bundle.VXMH5QUD.js", "file_uploader.bundle.js": "/assets/frappe/dist/js/file_uploader.bundle.MXMIUNUT.js", "kanban_board.bundle.js": "/assets/frappe/dist/js/kanban_board.bundle.2KHIS3VS.js", "desk.bundle.css": "/assets/frappe/dist/css/desk.bundle.6TTYX2YL.css", "email.bundle.css": "/assets/frappe/dist/css/email.bundle.DKKTEDW2.css", "login.bundle.css": "/assets/frappe/dist/css/login.bundle.Y3FR2QFL.css", "print.bundle.css": "/assets/frappe/dist/css/print.bundle.CAGZDDN5.css", "print_format.bundle.css": "/assets/frappe/dist/css/print_format.bundle.2HSAUKSY.css", "report.bundle.css": "/assets/frappe/dist/css/report.bundle.2LGHEWUB.css", "web_form.bundle.css": "/assets/frappe/dist/css/web_form.bundle.R3MWZXMZ.css", "website.bundle.css": "/assets/frappe/dist/css/website.bundle.WPPO2GSH.css", "bank-reconciliation-tool.bundle.js": "/assets/erpnext/dist/js/bank-reconciliation-tool.bundle.G7MHMAFO.js", "erpnext-web.bundle.js": "/assets/erpnext/dist/js/erpnext-web.bundle.253I7LT4.js", "erpnext.bundle.js": "/assets/erpnext/dist/js/erpnext.bundle.N56MLHSF.js", "item-dashboard.bundle.js": "/assets/erpnext/dist/js/item-dashboard.bundle.C2XRWYYU.js", "point-of-sale.bundle.js": "/assets/erpnext/dist/js/point-of-sale.bundle.SLDCH7JB.js", "bom_configurator.bundle.js": "/assets/erpnext/dist/js/bom_configurator.bundle.OPXUQA72.js", "erpnext-web.bundle.css": "/assets/erpnext/dist/css/erpnext-web.bundle.Y6VAQ5GM.css", "erpnext.bundle.css": "/assets/erpnext/dist/css/erpnext.bundle.NWMOHPQO.css", "erpnext_email.bundle.css": "/assets/erpnext/dist/css/erpnext_email.bundle.ZDF64QWV.css", "healthcare.bundle.js": "/assets/healthcare/dist/js/healthcare.bundle.FJSYAOAL.js"}